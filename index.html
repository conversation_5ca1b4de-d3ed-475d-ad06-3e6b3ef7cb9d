<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Calculator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="main-container">
        <!-- Calculator 1 -->
        <div class="calculator-container">
            <h1>Moons Batch</h1>
            <form id="production-form-1" class="production-form">
                <div class="form-group">
                    <label for="item-select-1">Select Item:</label>
                    <select id="item-select-1" name="item" class="item-select">
                        <option value="lf">LF</option>
                        <option value="hunter">Hunter</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="quantity-1">Quantity:</label>
                    <input type="text" id="quantity-1" name="quantity" class="quantity" min="1" value="1" required inputmode="numeric" pattern="[0-9,]*">
                </div>
                <button class="submit-btn" type="submit">Calculate</button>
            </form>
            <div id="results-container-1" class="results-container">
                <h2>Results</h2>
                <div class="result-line">
                    <p><strong>Metal:</strong> <span id="total-a-1" class="total-a">0</span></p>
                    <button class="copy-btn" title="Copy Metal amount">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Crystal:</strong> <span id="total-b-1" class="total-b">0</span></p>
                    <button class="copy-btn" title="Copy Crystal amount">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Deuterium:</strong> <span id="total-c-1" class="total-c">0</span></p>
                    <button class="copy-btn" title="Copy Deuterium amount">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Time:</strong> <span id="total-time-1" class="total-time">0</span></p>
                </div>
            </div>
        </div>

        <!-- Calculator 2 -->
        <div class="calculator-container">
            <h1>Calculator 2</h1>
            <form id="production-form-2" class="production-form">
                <div class="form-group">
                    <label for="item-select-2">Select Item:</label>
                    <select id="item-select-2" name="item" class="item-select">
                        <option value="lf">LF</option>
                        <option value="hunter">Hunter</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="quantity-2">Quantity:</label>
                    <input type="text" id="quantity-2" name="quantity" class="quantity" min="1" value="1" required inputmode="numeric" pattern="[0-9,]*">
                </div>
                <button class="submit-btn" type="submit">Calculate</button>
            </form>
            <div id="results-container-2" class="results-container">
                <h2>Results</h2>
                <div class="result-line">
                    <p><strong>Metal:</strong> <span id="total-a-2" class="total-a">0</span></p>
                    <button class="copy-btn" title="Copy Metal amount">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Crystal:</strong> <span id="total-b-2" class="total-b">0</span></p>
                    <button class="copy-btn" title="Copy Crystal amount">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Deuterium:</strong> <span id="total-c-2" class="total-c">0</span></p>
                    <button class="copy-btn" title="Copy Deuterium amount">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Time:</strong> <span id="total-time-2" class="total-time">0</span></p>
                </div>
            </div>
        </div>

        <!-- Ship Loss Calculator -->
        <div class="calculator-container">
            <h1>Ship Loss Calculator</h1>
            <form id="ship-loss-form" class="ship-loss-form">
                <div class="form-group">
                    <label for="ship-input">Enter Ship(s) and Loss:</label>
                    <input type="text" id="ship-input" name="ship-input" placeholder="e.g., Hunter 3000 -1500 Cruiser 2000 -100" required>
                    <small class="help-text">Format: ShipName TotalAmount -LossAmount (multiple ships separated by spaces)</small>
                </div>
                <button type="submit">Calculate Loss</button>
            </form>
            <div id="ship-results-container" class="results-container">
                <h2>Loss Results</h2>
                <div class="result-line">
                    <p><strong>Ship:</strong> <span id="ship-name">-</span></p>
                </div>
                <div class="result-line">
                    <p><strong>Lost Ships:</strong> <span id="ships-lost">0</span></p>
                    <button class="copy-btn" title="Copy ships lost">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Metal Lost:</strong> <span id="metal-lost">0</span></p>
                    <button class="copy-btn" title="Copy Metal lost">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Crystal Lost:</strong> <span id="crystal-lost">0</span></p>
                    <button class="copy-btn" title="Copy Crystal lost">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Deuterium Lost:</strong> <span id="deuterium-lost">0</span></p>
                    <button class="copy-btn" title="Copy Deuterium lost">📋</button>
                </div>
                <div class="result-line">
                    <p><strong>Total Loss Value:</strong> <span id="total-loss">0</span></p>
                    <button class="copy-btn" title="Copy Total loss value">📋</button>
                </div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>