document.addEventListener('DOMContentLoaded', () => {
    const quantityInputs = document.querySelectorAll('.quantity');

    quantityInputs.forEach(input => {
        // Function to clean and format the input value
        const formatQuantityInput = (inputValue) => {
            // Remove all non-digit characters (including existing commas)
            let cleanedValue = inputValue.replace(/[^0-9]/g, '');

            // Handle empty input
            if (cleanedValue === '') {
                return '';
            }

            // Convert to a number
            const numberValue = parseInt(cleanedValue, 10);

            // If it's not a valid number after cleaning, return empty string
            if (isNaN(numberValue)) {
                return '';
            }

            // Format the number with commas (e.g., 1,000)
            return numberValue.toLocaleString('en-US');
        };

        // Initial formatting if a value is present (e.g., from value="1" in HTML)
        if (input.value) {
            input.value = formatQuantityInput(input.value);
        }

        // Add input event listener for real-time formatting
        input.addEventListener('input', (event) => {
            const oldValue = input.value;
            const oldCursorPos = input.selectionStart;

            const formattedValue = formatQuantityInput(oldValue);

            input.value = formattedValue;

            // Calculate the difference in length caused by formatting
            // This helps in adjusting the cursor position correctly
            const lengthDiff = formattedValue.length - oldValue.length;

            // Adjust cursor position
            let newCursorPos = oldCursorPos + lengthDiff;

            // Ensure cursor doesn't go out of bounds
            newCursorPos = Math.max(0, Math.min(newCursorPos, formattedValue.length));

            // Set the new cursor position
            input.setSelectionRange(newCursorPos, newCursorPos);
        });

        // Add a blur event listener to ensure the value is a valid number
        // and to re-format if user manually removes commas or types invalid chars
        input.addEventListener('blur', () => {
            const formattedValue = formatQuantityInput(input.value);
            const numberValue = parseInt(formattedValue.replace(/[^0-9]/g, ''), 10);

            if (isNaN(numberValue) || numberValue < 1) {
                input.value = '1'; // Enforce min="1" requirement
            } else {
                input.value = formattedValue;
            }
        });
    });

    // --- Calculation and UI Logic ---

    // Data for item costs and build times (in seconds)
    const itemData = {
        lf: { metal: 2020,
            crystal: 670,
            deuterium: 0,
            time: 0.0028 },
        hunter: { metal: 104000,
            crystal: 57100,
            deuterium: 0,
            time: 0.08 }
    };

    // Ship data from ships.md (metal, crystal, deuterium)
    const ships = {
        'light fighter': { metal: 2000, crystal: 669, deuterium: 0 },
        'lf': { metal: 2000, crystal: 669, deuterium: 0 },
        'cruiser': { metal: 2000, crystal: 669, deuterium: 669 },
        'destroyer': { metal: 2700, crystal: 669, deuterium: 669 },
        'battleship': { metal: 2000, crystal: 669, deuterium: 669 },
        'exterminator': { metal: 4000, crystal: 2000, deuterium: 669 },
        'hunter': { metal: 104000, crystal: 56900, deuterium: 0 }
    };

    // Helper function to format total seconds into a d/h/m/s string
    const formatTime = (totalSeconds) => {
        if (isNaN(totalSeconds) || totalSeconds < 0) return "0s";
        if (totalSeconds === 0) return "0s";

        const days = Math.floor(totalSeconds / 86400);
        totalSeconds %= 86400;
        const hours = Math.floor(totalSeconds / 3600);
        totalSeconds %= 3600;
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);

        let timeString = '';
        if (days > 0) timeString += `${days}d `;
        if (hours > 0) timeString += `${hours}h `;
        if (minutes > 0) timeString += `${minutes}m `;
        if (seconds > 0 || timeString === '') timeString += `${seconds}s`;

        return timeString.trim();
    };

    // Main function to handle form submission and calculation
    const handleCalculation = (event) => {
        event.preventDefault(); // Prevent page reload on form submission
        const form = event.target;
        const formId = form.id.split('-').pop(); // Gets the '1' or '2' from the form's ID

        const itemSelect = form.querySelector('.item-select');
        const quantityInput = form.querySelector('.quantity');
        const selectedItemKey = itemSelect.value;

        // Clean the quantity input (remove commas) before converting to a number
        const quantity = parseInt(quantityInput.value.replace(/[^0-9]/g, ''), 10) || 0;

        if (itemData[selectedItemKey] && quantity > 0) {
            const item = itemData[selectedItemKey];
            const totalMetal = item.metal * quantity;
            const totalCrystal = item.crystal * quantity;
            const totalDeuterium = item.deuterium * quantity;
            const totalTime = item.time * quantity;

            // Update the UI, formatting large numbers with commas
            document.getElementById(`total-a-${formId}`).textContent = totalMetal.toLocaleString('en-US');
            document.getElementById(`total-b-${formId}`).textContent = totalCrystal.toLocaleString('en-US');
            document.getElementById(`total-c-${formId}`).textContent = totalDeuterium.toLocaleString('en-US');
            document.getElementById(`total-time-${formId}`).textContent = formatTime(totalTime);
        } else {
            // If input is invalid or zero, reset the results
            document.getElementById(`total-a-${formId}`).textContent = '0';
            document.getElementById(`total-b-${formId}`).textContent = '0';
            document.getElementById(`total-c-${formId}`).textContent = '0';
            document.getElementById(`total-time-${formId}`).textContent = '0s';
        }
    };

    // Attach the calculation handler to both forms
    document.querySelectorAll('.production-form').forEach(form => {
        form.addEventListener('submit', handleCalculation);
    });

    // Add functionality to all copy buttons
    document.querySelectorAll('.copy-btn').forEach(button => {
        button.addEventListener('click', (event) => {
            const btn = event.currentTarget;
            const resultLine = btn.closest('.result-line');
            const valueSpan = resultLine.querySelector('span');

            if (valueSpan) {
                const valueToCopy = valueSpan.textContent.replace(/,/g, '');
                navigator.clipboard.writeText(valueToCopy);
            }
        });
    });

    // --- Ship Loss Calculator ---
    const shipLossForm = document.getElementById('ship-loss-form');
    const shipInput = document.getElementById('ship-input');

    function parseShipInput(input) {
        // Remove extra spaces
        const cleanInput = input.trim();

        // Use regex to find all patterns of: ShipName Number -Number
        // This regex looks for: word(s) followed by number followed by negative number
        const regex = /([a-zA-Z\s]+?)\s+(\d+(?:,\d{3})*)\s+(-\d+(?:,\d{3})*)/g;
        const results = [];
        let match;

        while ((match = regex.exec(cleanInput)) !== null) {
            const shipName = match[1].trim().toLowerCase();
            const totalAmount = parseInt(match[2].replace(/,/g, ''), 10);
            const lossAmount = Math.abs(parseInt(match[3].replace(/,/g, ''), 10)); // Make positive

            results.push({ shipName, totalAmount, lossAmount });
        }

        return results.length > 0 ? results : null;
    }

    function calculateShipLoss() {
        const inputValue = shipInput.value.trim();

        if (!inputValue) {
            resetShipResults();
            return;
        }

        const parsedEntries = parseShipInput(inputValue);

        if (!parsedEntries) {
            resetShipResults();
            document.getElementById('ship-name').textContent = 'Invalid format';
            return;
        }

        let totalMetalLost = 0;
        let totalCrystalLost = 0;
        let totalDeuteriumLost = 0;
        let totalShipsLost = 0;
        let shipNames = [];
        let validEntries = 0;

        for (const entry of parsedEntries) {
            const { shipName, totalAmount, lossAmount } = entry;

            // Find ship in our database (try exact match first, then partial)
            let shipData = ships[shipName];
            if (!shipData) {
                // Try to find a partial match
                const shipKeys = Object.keys(ships);
                const foundKey = shipKeys.find(key =>
                    key.includes(shipName) || shipName.includes(key)
                );
                if (foundKey) {
                    shipData = ships[foundKey];
                }
            }

            if (shipData) {
                // Calculate losses for this ship
                const metalLost = shipData.metal * lossAmount;
                const crystalLost = shipData.crystal * lossAmount;
                const deuteriumLost = shipData.deuterium * lossAmount;

                // Add to totals
                totalMetalLost += metalLost;
                totalCrystalLost += crystalLost;
                totalDeuteriumLost += deuteriumLost;
                totalShipsLost += lossAmount;

                // Add ship name to list (capitalize first letter)
                const formattedName = shipName.charAt(0).toUpperCase() + shipName.slice(1);
                shipNames.push(`${formattedName} (${lossAmount.toLocaleString()})`);
                validEntries++;
            }
        }

        if (validEntries === 0) {
            resetShipResults();
            document.getElementById('ship-name').textContent = 'No valid ships found';
            return;
        }

        const totalLossValue = totalMetalLost + totalCrystalLost + totalDeuteriumLost;

        // Update UI
        document.getElementById('ship-name').textContent = shipNames.join(', ');
        document.getElementById('ships-lost').textContent = totalShipsLost.toLocaleString();
        document.getElementById('metal-lost').textContent = totalMetalLost.toLocaleString();
        document.getElementById('crystal-lost').textContent = totalCrystalLost.toLocaleString();
        document.getElementById('deuterium-lost').textContent = totalDeuteriumLost.toLocaleString();
        document.getElementById('total-loss').textContent = totalLossValue.toLocaleString();
    }

    function resetShipResults() {
        document.getElementById('ship-name').textContent = '-';
        document.getElementById('ships-lost').textContent = '0';
        document.getElementById('metal-lost').textContent = '0';
        document.getElementById('crystal-lost').textContent = '0';
        document.getElementById('deuterium-lost').textContent = '0';
        document.getElementById('total-loss').textContent = '0';
    }

    // Event listeners for ship loss calculator
    shipLossForm.addEventListener('submit', (event) => {
        event.preventDefault();
        calculateShipLoss();
    });

    shipInput.addEventListener('input', calculateShipLoss);

    // Initial calculation
    calculateShipLoss();
});