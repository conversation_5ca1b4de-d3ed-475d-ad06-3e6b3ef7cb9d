/**
 * Formats a total number of seconds into a human-readable string
 * showing days, hours, minutes, and seconds.
 * @param {number} totalSeconds The total time in seconds.
 * @returns {string} Formatted time string (e.g., "1 day, 2 hours, 30 minutes, 5 seconds").
 */
function formatTime(totalSeconds) {
    if (totalSeconds === 0) {
        return "0 seconds";
    }

    const days = Math.floor(totalSeconds / (24 * 3600));
    totalSeconds %= (24 * 3600);
    const hours = Math.floor(totalSeconds / 3600);
    totalSeconds %= 3600;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    let parts = [];
    if (days > 0) parts.push(`${days} day${days > 1 ? 's' : ''}`);
    if (hours > 0) parts.push(`${hours} hour${hours > 1 ? 's' : ''}`);
    if (minutes > 0) parts.push(`${minutes} minute${minutes > 1 ? 's' : ''}`);
    // Only include seconds if there are any, or if it's the only unit (e.g., "5 seconds")
    if (seconds > 0 || parts.length === 0) parts.push(`${seconds} second${seconds > 1 ? 's' : ''}`);

    return parts.join(', ');
}

document.addEventListener('DOMContentLoaded', () => {
    // Data for the items that can be produced
    const items = {
        lf: {
            name: 'LF',
            a: 2100,
            b: 700,
            c: 0,
            time: 1
        },
        hunter: {
            name: 'Hunter',
            a: 109000,
            b: 59500,
            c: 0,
            time: 4
        },
        aid: {
            name: 'Aid',
            a: 438000,
            b: 238000,
            c: 0,
            time: 8
        },
        bw: {
            name: 'Black Wanderer',
            a: 280000,
            b: 140000,
            c: 52500,
            time: 6
        },
    };

// Get references to the HTML elements (using IDs as per the provided index.html)
    const form = document.getElementById('production-form'); // The form element
    const itemSelect = document.getElementById('item-select'); // The item selection dropdown
    const quantityInput = document.getElementById('quantity'); // The quantity input field
    
    const totalAEl = document.getElementById('total-a'); // Span for total resource A
    const totalBEl = document.getElementById('total-b'); // Span for total resource B
    const totalCEl = document.getElementById('total-c'); // Span for total resource C
    const totalTimeEl = document.getElementById('total-time'); // Span for total time

    /**
     * Calculates the total resources and time based on selected item and quantity,
     * then updates the UI.
     */
    function calculateAndDisplay() {
        const selectedItemKey = itemSelect.value; // Get the currently selected item's key
        const quantity = parseInt(quantityInput.value, 10); // Get the quantity as an integer

        // Basic validation: if quantity is not a number or less than 1, reset display and exit
        if (isNaN(quantity) || quantity < 1) {
            totalAEl.textContent = '0';
            totalBEl.textContent = '0';
            totalCEl.textContent = '0';
            totalTimeEl.textContent = '0 seconds'; // Reset formatted time
            totalTimeEl.dataset.rawTime = '0'; // Reset raw time for copying
            return; // Stop execution if input is invalid
        }

        const item = items[selectedItemKey]; // Get the data for the selected item

        // Calculate totals for resources and time
        const totalA = item.a * quantity;
        const totalB = item.b * quantity;
        const totalC = item.c * quantity;
        const totalTimeSeconds = item.time * quantity; // Keep time in raw seconds for calculations

        // Update the text content of the result spans, formatting numbers and time
        totalAEl.textContent = totalA.toLocaleString(); // Format with local thousands separators
        totalBEl.textContent = totalB.toLocaleString();
        totalCEl.textContent = totalC.toLocaleString();
        totalTimeEl.textContent = formatTime(totalTimeSeconds); // Display time in formatted string
        totalTimeEl.dataset.rawTime = totalTimeSeconds; // Store raw seconds in a data attribute for copying
    }

    // --- Event Listeners ---
    // Listen for form submission (e.g., pressing Enter or clicking a submit button)
    form.addEventListener('submit', (event) => {
        event.preventDefault(); // Prevent the default form submission behavior (page reload)
        calculateAndDisplay(); // Recalculate and display results
    });

    // Listen for any input changes in the form (e.g., changing item or quantity)
    form.addEventListener('input', calculateAndDisplay);

    // Perform an initial calculation when the page loads to show default values
    calculateAndDisplay();

    // --- Clipboard Functionality ---
    // Select all elements with the class 'copy-btn'
    const copyButtons = document.querySelectorAll('.copy-btn');

    // Attach a click event listener to each copy button
    copyButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Get the parent 'result-line' div and find the span containing the value
            const resultLine = button.parentElement; 
            const valueSpan = resultLine.querySelector('p > span'); 
            let valueToCopy;

            // If it's the time span, copy the raw seconds stored in data-raw-time
            if (valueSpan.id === 'total-time' && valueSpan.dataset.rawTime) {
                valueToCopy = valueSpan.dataset.rawTime;
            } else {
                // For other resource spans, get the text content and remove commas
                valueToCopy = valueSpan.textContent.replace(/,/g, '');
            }

            navigator.clipboard.writeText(valueToCopy).then(() => {
                // Visual feedback for the user
                const originalContent = button.innerHTML;
                const originalTitle = button.title;
                button.innerHTML = '✅';
                button.title = 'Copied!';
                setTimeout(() => {
                    button.innerHTML = originalContent;
                    button.title = originalTitle;
                }, 1500);
            }).catch(err => {
                console.error('Failed to copy to clipboard: ', err);
            });
        });
    });
});