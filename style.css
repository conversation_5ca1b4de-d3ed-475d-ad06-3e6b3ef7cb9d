body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: #1a1a2e;
    /* Dark blue/purple */
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    margin: 0;
    padding: 2rem;
    color: #e0e0e0;
    /* Light gray text */
}

.submit-btn {
    margin-top: 1rem;
}

.main-container {
    display: flex;
    flex-wrap: wrap;
    /* Allow wrapping on smaller screens */
    justify-content: center;
    gap: 2rem;
    width: 100%;
    max-width: 1200px;
}

.calculator-container {
    background: #fff;
    background: #16213e;
    /* Darker blue */
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    width: 100%;
    max-width: 400px;
    border: 1px solid #0f3460;
}

h1 {
    color: #e94560;
    /* Accent color */
    margin-top: 0;
}

h2 {
    color: #34495e;
    border-bottom: 1px solid #ecf0f1;
    color: #c0c0c0;
    border-bottom: 1px solid #3a3a5e;
    padding-bottom: 0.5rem;
    margin-top: 2rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
    color: #a0a0c0;
    font-weight: bold;
}

input[type="number"],
input[type="text"],
select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #3a3a5e;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 1rem;
    background-color: #1a1a2e;
    color: #e0e0e0;
}

.help-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.85rem;
    color: #a0a0c0;
    font-style: italic;
}

button {
    width: 100%;
    padding: 0.75rem;
    background-color: #3498db;
    background-color: #e94560;
    /* Accent color */
    color: white;
    border: none;
    border-radius: 4px;
}

button:hover {
    background-color: #2980b9;
    background-color: #c73049;
}

.results-container span {
    font-weight: bold;
    color: #2980b9;
    color: #53a8b6;
    /* Different accent for results */
}

.result-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
}

.result-line p {
    font-size: 1.1rem;
    display: flex;
    justify-content: space-between;
    flex-grow: 1;
    margin: 0;
}

.copy-btn {
    /* Reset some of the global button styles */
    width: auto;
    padding: 0.25rem 0.5rem;
    background-color: transparent;
    border: none;
    color: #a0a0c0;
    font-size: 1.2rem;
    margin-left: 1rem;
    cursor: pointer;
    transition: color 0.2s ease, transform 0.2s ease;
}

.copy-btn:hover {
    background-color: transparent;
    /* Explicitly override hover */
    color: #e94560;
    /* Accent color */
    transform: scale(1.1);
}

.results-container span {
    font-weight: bold;
    color: #53a8b6;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    body {
        padding: 1rem;
    }

    .main-container {
        gap: 1rem;
    }

    .calculator-container {
        max-width: 100%;
        min-width: 300px;
    }
}